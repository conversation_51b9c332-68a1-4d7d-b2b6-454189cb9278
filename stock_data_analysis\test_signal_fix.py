#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的买卖点标记功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from indicator_analysis import StockEchartVisualizer

def test_signal_fix():
    """测试修复后的信号检测"""
    print("=== 测试修复后的买卖点标记 ===")
    
    # 创建可视化器
    visualizer = StockEchartVisualizer()
    
    # 测试豆粕ETF
    ts_code = "159985.SZ"
    print(f"\n正在分析 {ts_code} (豆粕ETF)...")
    
    try:
        # 生成图表
        chart = visualizer.plot_stock_chart(
            ts_code=ts_code,
            display_points=200,  # 显示200个数据点
            force_refresh=False,
            long_term=26,
            short_term=13,
            add_trendlines=True,
            show_breakthrough=True,
            add_fibonacci=True,
            detect_patterns=True
        )
        
        if chart is not None:
            # 保存图表
            output_file = current_dir / f"test_signals_{ts_code.replace('.', '_')}.html"
            chart.render(str(output_file))
            print(f"图表已保存到: {output_file}")
            print("请打开HTML文件查看修复后的买卖点标记")
            
            # 打开浏览器查看
            import webbrowser
            webbrowser.open(f"file://{output_file.absolute()}")
            
        else:
            print("图表生成失败")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_signal_fix()
