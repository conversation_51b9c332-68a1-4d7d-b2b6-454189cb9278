#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证买卖点标记的合理性
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from indicator_analysis import StockDataManager, SignalDetector, TechnicalIndicator
from MyTT import EMA

def validate_signals():
    """验证买卖点标记的合理性"""
    print("=== 验证买卖点标记的合理性 ===")
    
    # 创建数据管理器
    data_manager = StockDataManager()
    
    # 测试豆粕ETF
    ts_code = "159985.SZ"
    print(f"\n正在分析 {ts_code} (豆粕ETF)...")
    
    try:
        # 获取数据
        df = data_manager.get_fund_data(ts_code, force_refresh=False)
        
        if df.empty:
            print("数据为空")
            return
            
        # 计算技术指标
        df['ema_long'] = EMA(df['close'], 26)
        df['ema_short'] = EMA(df['close'], 13)
        
        # 计算动量
        df['ema_diff'] = df['ema_short'] - df['ema_long']
        ema_diff_series = pd.Series(df['ema_diff'])
        ema_diff_change = ema_diff_series.diff()
        df['momentum'] = TechnicalIndicator.smooth_data(ema_diff_change.fillna(0), window_size=5)
        
        # 检测信号
        buy_df, sell_df, quality_stats = SignalDetector.detect_enhanced_signals(
            df, ema_short_col='ema_short', ema_long_col='ema_long',
            momentum_col='momentum', volume_col='volume'
        )
        
        print(f"\n=== 信号统计 ===")
        print(f"买入信号数量: {quality_stats['buy_signal_count']}")
        print(f"卖出信号数量: {quality_stats['sell_signal_count']}")
        print(f"平均买入信号质量: {quality_stats['avg_buy_quality']:.3f}")
        print(f"平均卖出信号质量: {quality_stats['avg_sell_quality']:.3f}")
        
        # 分析买入信号的位置
        if not buy_df.empty:
            print(f"\n=== 买入信号分析 ===")
            for idx, row in buy_df.iterrows():
                date = row['trade_date']
                price = row['close']
                
                # 计算价格在近期的分位数
                recent_prices = df.loc[max(0, idx-50):idx, 'close']
                percentile = (recent_prices <= price).mean() * 100
                
                print(f"买入信号: {date}, 价格: {price:.3f}, 近50日分位数: {percentile:.1f}%")
                
        # 分析卖出信号的位置
        if not sell_df.empty:
            print(f"\n=== 卖出信号分析 ===")
            for idx, row in sell_df.iterrows():
                date = row['trade_date']
                price = row['close']
                
                # 计算价格在近期的分位数
                recent_prices = df.loc[max(0, idx-50):idx, 'close']
                percentile = (recent_prices <= price).mean() * 100
                
                print(f"卖出信号: {date}, 价格: {price:.3f}, 近50日分位数: {percentile:.1f}%")
        
        # 检查是否在极值点产生错误信号
        print(f"\n=== 极值点检查 ===")
        
        # 找到价格的极大值和极小值
        from scipy.signal import argrelextrema
        price_data = df['close'].values
        maxima = argrelextrema(price_data, np.greater, order=10)[0]
        minima = argrelextrema(price_data, np.less, order=10)[0]
        
        # 检查买入信号是否在极大值附近
        buy_at_peaks = 0
        if not buy_df.empty:
            for idx in buy_df.index:
                for peak_idx in maxima:
                    if abs(idx - peak_idx) <= 3:  # 3天内
                        buy_at_peaks += 1
                        print(f"警告: 买入信号在极大值附近 - 日期: {df.iloc[idx]['trade_date']}")
                        
        # 检查卖出信号是否在极小值附近
        sell_at_troughs = 0
        if not sell_df.empty:
            for idx in sell_df.index:
                for trough_idx in minima:
                    if abs(idx - trough_idx) <= 3:  # 3天内
                        sell_at_troughs += 1
                        print(f"警告: 卖出信号在极小值附近 - 日期: {df.iloc[idx]['trade_date']}")
        
        print(f"\n=== 验证结果 ===")
        print(f"在极大值附近的买入信号: {buy_at_peaks}")
        print(f"在极小值附近的卖出信号: {sell_at_troughs}")
        
        if buy_at_peaks == 0 and sell_at_troughs == 0:
            print("验证通过：没有在极值点产生错误信号")
        else:
            print("验证失败：在极值点产生了错误信号")
            
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validate_signals()
